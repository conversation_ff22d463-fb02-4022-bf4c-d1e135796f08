package middleware

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	sharedSecretKey    = "SECRET" // TODO: Replace with a secure key from config
	timestampHeader    = "X-Timestamp"
	signatureHeader    = "X-Signature"
	acceptableTimeSkew = 1 * time.Minute // 允许客户端与服务器时间戳有1分钟的偏差
)

// calculateHMACSHA256 计算给定数据的 HMAC-SHA256 签名
func calculateHMACSHA256(data []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(data)
	return hex.EncodeToString(h.Sum(nil))
}

// SignatureAuthMiddleware 返回一个 Gin 中间件用于校验请求签名
func SignatureAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 获取原始请求体
		rawBody, err := io.ReadAll(c.Request.Body)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "Failed to read request body"})
			return
		}
		// 读取后，需要将body重新放回去，以便后续的处理器可以使用
		c.Request.Body = io.NopCloser(bytes.NewBuffer(rawBody))

		// 2. 获取鉴权相关的请求头
		clientTimestampStr := c.GetHeader(timestampHeader)
		clientSignature := c.GetHeader(signatureHeader)

		if clientTimestampStr == "" || clientSignature == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Missing timestamp or signature headers"})
			return
		}

		// 3. 校验时间戳
		clientTimestampUnix, err := strconv.ParseInt(clientTimestampStr, 10, 64)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid timestamp format"})
			return
		}

		serverTimestampUnix := time.Now().Unix()
		if (serverTimestampUnix-clientTimestampUnix) > int64(acceptableTimeSkew.Seconds()) ||
			(clientTimestampUnix-serverTimestampUnix) > int64(acceptableTimeSkew.Seconds()) {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Timestamp is out of acceptable range"})
			return
		}

		// 4. 准备待签名的数据 (原始请求体 + 时间戳字符串)
		dataToSign := append(rawBody, []byte(clientTimestampStr)...)

		// 5. 计算预期签名
		expectedSignature := calculateHMACSHA256(dataToSign, sharedSecretKey)

		// 6. 安全比较签名
		// hmac.Equal 用于防止时序攻击
		isValidSignature := hmac.Equal([]byte(clientSignature), []byte(expectedSignature))

		if !isValidSignature {
			// log.Printf("Auth failed. Got Sig: %s, Expected Sig: %s, DataSigned: %s", clientSignature, expectedSignature, dataToSign) // For debugging
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
			return
		}

		c.Next()
	}
}
