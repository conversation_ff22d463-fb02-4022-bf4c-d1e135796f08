package proc

import (
	"context"
	"sync"
	"tasrv/internal/logic/ck"

	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"github.com/sirupsen/logrus"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/ta"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

type TaService struct {
}

var (
	once     = &sync.Once{}
	instance *TaService
)

func GetTaServiceInstance() *TaService {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &TaService{}
	})
	return instance
}

// DataReportReq 处理数据上报请求
func (h *TaService) DataReportReq(ctx context.Context, req *taPB.DataReportReq) *taPB.DataReportRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &taPB.DataReportRsp{}

	// 开关
	open := cmodel.GetDataReport(int64(req.ReportType), consul_config.WithGrpcCtx(ctx))
	if open.Open != int32(commonPB.EXPRESSION_TYPE_ET_YES) {
		entry.Warnf("data switch is off, report type: %s", req.ReportType.String())
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
		return rsp
	}

	// 异步上报
	safego.Go(func() {
		err := ck.ProcessAndPublish(context.Background(), req)
		if err != nil {
			logrus.Errorf("data report failed, err: %s, req:%+v", err, req)
		}
		logrus.Debugf("data report success, msg: %+v", req)
	})

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp
}
