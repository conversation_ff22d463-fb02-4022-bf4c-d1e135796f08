package proc

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/ta"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
)

// TaHandler 数据上报相关
type TaHandler struct {
}

func GetTaInstance() *TaHandler {
	return &TaHandler{}
}

// DataReportReq 处理数据上报请求
func (h *TaHandler) DataReportReq(ctx context.Context, _ *intranetGrpc.Header, req *taPB.DataReportReq) *transport.ResponseMsg {
	rsp := GetTaServiceInstance().DataReportReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_TA_DATA_REPORT_RSP, rsp)
}
