package apis

import (
	"context"
	"net/http"
	model "tasrv/internal/model/clickhouse"
	"tasrv/internal/pubsub"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// PlayerOpenReport 处理客户端上报数据的请求
func PlayerOpenReport(c *gin.Context) {
	entry := logx.NewLogEntry(c)
	var req model.PlayerOpenReportRequest
	rsp := model.PlayerOpenReportResponse{
		Code: int(commonPB.ErrCode_ERR_SUCCESS),
	}
	ctx := context.Background()

	if err := c.ShouldBindJSON(&req); err != nil {
		entry.Errorf("PlayerOpenReport: bind request error: %v", err)
		rsp.Code = int(commonPB.ErrCode_ERR_FAIL)
		rsp.Message = err.Error()
		c.JSON(http.StatusBadRequest, rsp)
		return
	}

	entry.Infof("PlayerOpenReport req=%+v", req)

	// 开关
	open := cmodel.GetDataReport(int64(commonPB.DATA_REPORT_TYPE_DRT_APP_OPEN), consul_config.WithGrpcCtx(ctx))
	if open.Open != int32(commonPB.EXPRESSION_TYPE_ET_YES) {
		entry.Infof("PlayerOpenReport: data switch is off, ModuleType: %d, Open: %d",
			commonPB.DATA_REPORT_TYPE_DRT_APP_OPEN, open)
		rsp.Code = int(commonPB.ErrCode_ERR_SUCCESS)
		c.JSON(http.StatusOK, rsp)
		return
	}

	msg := model.RLaunchStep{
		PlayerOpenReportRequest: req,
	}

	if req.TableName != msg.GetTableName() || req.NowDate == "" || req.TimeStampValue == "" || req.DeviceCode == "" {
		logrus.Errorf("PlayerOpenReport: param is not valid, TableName: %s, NowDate: %s, TimeStampValue: %s, DeviceCode: %s",
			req.TableName, req.NowDate, req.TimeStampValue, req.DeviceCode)
		rsp.Code = int(commonPB.ErrCode_ERR_FAIL)
		rsp.Message = "param is not valid"
		c.JSON(http.StatusBadRequest, rsp)
		return
	}

	safego.Go(func() {
		err := pubsub.GetProducer().SendWithTopic(context.Background(), msg.GetTableName(), event.NewMessage(transform.Uint642Str(req.PlayerId), []byte(msg.Format())))
		if err != nil {
			logrus.Errorf("PlayerOpenReport : Failed to submit report (produce error), PlayerId: %d, Topic: %s, error: %v",
				req.PlayerId, msg.GetTableName(), err)
			rsp.Code = int(commonPB.ErrCode_ERR_FAIL)
			rsp.Message = "Failed to submit report (produce error)"
			c.JSON(http.StatusInternalServerError, rsp)
			return
		}
	})

	c.JSON(http.StatusOK, rsp)
}
