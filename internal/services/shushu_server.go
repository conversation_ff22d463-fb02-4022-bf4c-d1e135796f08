package services

import (
	"encoding/base64"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"math/rand"
	"os"
	"tasrv/internal/model"
	"time"

	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"

	"github.com/ThinkingDataAnalytics/go-sdk/thinkingdata"
	"github.com/sirupsen/logrus"
)

// 上报开关
var reportSwitch struct {
	Track       bool `mapstructure:"track"`
	UserSet     bool `mapstructure:"user_set"`
	UserSetOnce bool `mapstructure:"user_set_once"`
}

func Init() {
	rsMap := viper.GetStringMap(configReportSwitchName)
	err := mapstructure.Decode(rsMap, &reportSwitch)
	if err != nil {
		logrus.Errorf("init reportSwitch error, data:%+v, err:%s", rsMap, err)
	}
	logrus.Infof("init reportSwitch %+v", reportSwitch)
}

const (
	logDiff                = 100000
	logDirectory           = "/data/shushulog"
	configReportSwitchName = "report_switch"
)

var (
	trackIndex int64
	setIndex   int64
)

type ShuShuService struct{}

var GTeService *teService

func StartShuShu() {
	trackIndex = 0
	setIndex = 0
	_, _err := os.Stat(logDirectory)
	if os.IsNotExist(_err) {
		err := os.MkdirAll(logDirectory, os.ModePerm)
		if err != nil {
			logrus.Fatalf("create dir error %v", err.Error())
		}
	}

	str, _ := generateRandomString(10)
	prefix := fmt.Sprintf("%v_%v", str, time.Now().UnixMilli())
	hostName := os.Getenv("HOSTNAME")
	if len(hostName) > 0 {
		prefix = hostName + "_" + prefix
	}

	logrus.Infof("te prefix: %v", prefix)

	// 创建 LogConfig 配置文件
	config := thinkingdata.LogConfig{
		Directory:      logDirectory, // 事件采集的文件路径
		RotateMode:     thinkingdata.ROTATE_HOURLY,
		FileNamePrefix: prefix,
	}
	// 初始化 logConsumer
	consumer, _ := thinkingdata.NewLogConsumerWithConfig(config)
	// 创建 te 对象
	te := thinkingdata.New(consumer)

	z, offset := time.Now().Zone()
	logrus.Infof("time zone %v, offset: %v", z, offset/3600)

	zoneOffset := offset / 3600

	GTeService = &teService{zoneOffset: zoneOffset, te: te}
}

func generateRandomString(length int) (string, error) {
	buffer := make([]byte, length)
	_, err := rand.Read(buffer)
	if err != nil {
		return "", err
	}
	randomString := base64.URLEncoding.EncodeToString(buffer)
	randomString = randomString[:length]
	return randomString, nil
}

func TEClose() {
	if GTeService != nil {
		GTeService.Close()
	}
}

type teService struct {
	zoneOffset int
	te         thinkingdata.TDAnalytics
}

func (s *teService) Close() {
	_err := s.te.Flush()
	if _err != nil {
		logrus.Warnf("Failed to flush te: %v", _err.Error())
	}
	_err = s.te.Close()
	if _err != nil {
		logrus.Warnf("Failed to close te: %v", _err.Error())
	}
}

func (s *teService) Track(playerId, eventName string, data model.IToTeMap, timestamp int64) {
	if !reportSwitch.Track {
		logrus.Infof("te switch off, eventName: %v playerId: %s", eventName, playerId)
		return
	}

	m := data.ToMap()

	m["#zone_offset"] = s.zoneOffset

	t := model.TETime(timex.Now())
	m["birthday"] = t.String()

	if timestamp > 0 {
		m["#time"] = time.Unix(timeToUnix(timestamp), 0)
	} else {
		m["#time"] = timex.Now().Unix()
	}

	if ip, ok := m["ip"]; ok && ip != "" {
		m["#ip"] = ip
	}

	if trackIndex%logDiff == 0 {
		str := fmt.Sprintf("%v, index: %v", eventName, trackIndex)
		defer Cost(timex.Now(), str)
	}
	trackIndex++

	err := s.te.Track(playerId, "", eventName, m)
	if err != nil {
		logrus.Warnf("track %v, data: %+v, error: %v", eventName, data, err)
	}
}

func (s *teService) UserSet(playerId string, data model.IToTeMap) {
	if !reportSwitch.UserSet {
		logrus.Infof("te userSet switch off, playerId=%s data: %+v", playerId, data.ToMap())
		return
	}
	m := data.ToMap()

	if setIndex%logDiff == 0 {
		str := fmt.Sprintf("%v, index: %v", "UserSet", setIndex)
		defer Cost(timex.Now(), str)
	}
	setIndex++

	err := s.te.UserSet(playerId, "", m)
	if err != nil {
		logrus.Warnf("userSet data: %+v, error: %v", m, err)
	}
}

func (s *teService) UserSetOnce(playerId string, data model.IToTeMap) {

	m := data.ToMap()

	if ip, ok := m["ip"]; ok && ip != "" {
		m["#ip"] = ip
	}

	err := s.te.UserSet(playerId, "", m)
	if err != nil {
		logrus.Warnf("userSetOnce data: %+v, error: %v", m, err)
	}
}

func (s *teService) UserAdd(playerId string, data model.IToTeMap) {

	m := data.ToMap()
	if setIndex%logDiff == 0 {
		str := fmt.Sprintf("%v, index: %v", "UserAdd", trackIndex)
		defer Cost(timex.Now(), str)
	}
	setIndex++
	err := s.te.UserAdd(playerId, "", m)
	if err != nil {
		logrus.Warnf("userAdd data: %+v, error: %v", m, err)
	}
}

func Cost(begin time.Time, tips string) {
	logrus.Infof("%v cost: %v milli", tips, timex.Now().UnixMilli()-begin.UnixMilli())
}

func timeToUnix(nSec int64) int64 {
	sec := nSec
	if (nSec / 1e12) == 1 {
		sec = nSec / 1000
	}
	return sec
}
