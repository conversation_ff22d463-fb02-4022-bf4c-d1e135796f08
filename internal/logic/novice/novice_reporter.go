package login

import (
	"context"
	"tasrv/internal/model"
	"tasrv/internal/services"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/spf13/cast"
)

func ProcessNoviceMessage(ctx context.Context, TeNoviceInfo *model.TeNovice) error {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ProcessNoviceMessage: %+v", *TeNoviceInfo)

	// 转换上报信息
	record := model.TeNoviceRecord{
		DefaultHeader: analyze.NewTeDefaultHeaderFromCtx(ctx),
		TeNovice:      TeNoviceInfo,
	}
	playerId := cast.ToString(TeNoviceInfo.PlayerId)

	services.GTeService.Track(playerId, record.GetEvent(), &record, timex.Now().Unix())
	return nil
}
