package item

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_hall"
	"tasrv/internal/model"
	"tasrv/internal/services"
)

// TokenGetAggregator 金币获得聚合器
type TokenGetAggregator struct {
	BaseEventAggregator
	totalCount int64
	source     int32
	hasData    bool
}

func NewTokenGetAggregator() *TokenGetAggregator {
	return &TokenGetAggregator{
		BaseEventAggregator: BaseEventAggregator{eventName: a_hall.EventTokenGet},
	}
}

func (a *TokenGetAggregator) AddItem(teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
	a.totalCount += itemInfo.ItemDeltaCount
	a.source = int32(teItem.Source)
	a.hasData = true
}

func (a *TokenGetAggregator) HasData() bool {
	return a.hasData
}

func (a *TokenGetAggregator) Report(playerId string, teItem *a_hall.TeItemInfo) {
	record := &model.TeTokenGetRecord{
		DefaultHeader: teItem.DefaultHeader,
		TokenCount:    a.totalCount,
		Source:        a.source,
		OperateTime:   teItem.OperateTime,
	}
	services.GTeService.Track(playerId, a.GetEventName(), record, teItem.OperateTime.Unix())
}

// 金币获得处理器
type TokenGetProcessor struct{}

func (p *TokenGetProcessor) ShouldProcess(teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) bool {
	return teItem.ChangeType == commonPB.ITEM_OPERATION_IO_ADD && itemInfo.Item.ItemType == commonPB.ITEM_TYPE_IT_CURRENCY_COIN
}

func (p *TokenGetProcessor) CollectData(aggregators map[string]EventAggregator, teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
	agg, ok := aggregators[a_hall.EventTokenGet]
	if !ok {
		agg = NewTokenGetAggregator()
		aggregators[a_hall.EventTokenGet] = agg
	}
	agg.AddItem(teItem, itemInfo)
}
