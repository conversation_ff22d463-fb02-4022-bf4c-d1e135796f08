package item

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_hall"
	"tasrv/internal/model"
	"tasrv/internal/services"
)

// ItemConsumeAggregator 道具消耗聚合器
type ItemConsumeAggregator struct {
	BaseEventAggregator
}

func NewItemConsumeAggregator() *ItemConsumeAggregator {
	return &ItemConsumeAggregator{
		BaseEventAggregator: BaseEventAggregator{eventName: a_hall.EventItemConsume},
	}
}

func (a *ItemConsumeAggregator) AddItem(teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
}

func (a *ItemConsumeAggregator) HasData() bool {
	return true
}

func (a *ItemConsumeAggregator) Report(playerId string, teItem *a_hall.TeItemInfo) {
	items := make([]*model.TeItem, 0, len(teItem.Items))
	for _, v := range teItem.Items {
		items = append(items, &model.TeItem{
			Item:           v.Item,
			ItemCount:      v.ItemCount,
			ItemDeltaCount: v.ItemDeltaCount,
		})
	}
	record := model.TeItemConsumeRecord{
		DefaultHeader: teItem.DefaultHeader,
		TeItemInfo: &model.TeItemInfo{
			Items:       items,
			Source:      teItem.Source,
			ChangeType:  teItem.ChangeType,
			OperateTime: teItem.OperateTime,
		},
	}

	services.GTeService.Track(playerId, a.GetEventName(), &record, teItem.OperateTime.Unix())
}

// 道具消耗处理器
type ItemConsumeProcessor struct{}

func (p *ItemConsumeProcessor) ShouldProcess(teItem *a_hall.TeItemInfo, _ *commonPB.ItemInfo) bool {
	return teItem.ChangeType == commonPB.ITEM_OPERATION_IO_REDUCE
}

func (p *ItemConsumeProcessor) CollectData(aggregators map[string]EventAggregator, teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
	agg, ok := aggregators[a_hall.EventItemConsume]
	if !ok {
		agg = NewItemConsumeAggregator()

		aggregators[a_hall.EventItemConsume] = agg
	}
	agg.AddItem(teItem, itemInfo)
}
