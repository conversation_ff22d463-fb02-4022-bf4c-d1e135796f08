package item

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_hall"
	"tasrv/internal/model"
	"tasrv/internal/services"
)

// ShopBuyAggregator 商店购买聚合器
type ShopBuyAggregator struct {
	BaseEventAggregator
}

func NewShopBuyAggregator() *ShopBuyAggregator {
	return &ShopBuyAggregator{
		BaseEventAggregator: BaseEventAggregator{eventName: a_hall.EventShopBuy},
	}
}

func (a *ShopBuyAggregator) AddItem(teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
}

func (a *ShopBuyAggregator) HasData() bool {
	return true
}

func (a *ShopBuyAggregator) Report(playerId string, teItem *a_hall.TeItemInfo) {

	items := make([]*model.TeItem, 0, len(teItem.Items))
	for _, v := range teItem.Items {
		items = append(items, &model.TeItem{
			Item:           v.Item,
			ItemCount:      v.ItemCount,
			ItemDeltaCount: v.ItemDeltaCount,
		})
	}
	record := model.TeShopBuyRecord{
		DefaultHeader: teItem.DefaultHeader,
		TeItemInfo: &model.TeItemInfo{
			Items:       items,
			Source:      teItem.Source,
			ChangeType:  teItem.ChangeType,
			OperateTime: teItem.OperateTime,
		},
	}

	services.GTeService.Track(playerId, a.GetEventName(), &record, teItem.OperateTime.Unix())
}

// 商店购买处理器
type ShopBuyProcessor struct{}

func (p *ShopBuyProcessor) ShouldProcess(teItem *a_hall.TeItemInfo, _ *commonPB.ItemInfo) bool {
	return teItem.Source == commonPB.ITEM_SOURCE_TYPE_IST_STORE_BUY
}

func (p *ShopBuyProcessor) CollectData(aggregators map[string]EventAggregator, teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
	agg, ok := aggregators[a_hall.EventShopBuy]
	if !ok {
		agg = NewShopBuyAggregator()
		aggregators[a_hall.EventShopBuy] = agg
	}
	agg.AddItem(teItem, itemInfo)
}
