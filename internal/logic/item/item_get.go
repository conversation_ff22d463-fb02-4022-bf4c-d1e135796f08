package item

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_hall"
	"tasrv/internal/model"
	"tasrv/internal/services"
)

// 道具获得处理器
type ItemGetProcessor struct{}

func (p *ItemGetProcessor) ShouldProcess(teItem *a_hall.TeItemInfo, _ *commonPB.ItemInfo) bool {
	return teItem.ChangeType == commonPB.ITEM_OPERATION_IO_ADD
}

func (p *ItemGetProcessor) CollectData(aggregators map[string]EventAggregator, teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
	agg, ok := aggregators[a_hall.EventItemGet]
	if !ok {
		agg = NewItemGetAggregator()
		aggregators[a_hall.EventItemGet] = agg
	}
	agg.AddItem(teItem, itemInfo)
}

// ItemGetAggregator 道具获得聚合器
type ItemGetAggregator struct {
	BaseEventAggregator
}

func NewItemGetAggregator() *ItemGetAggregator {
	return &ItemGetAggregator{
		BaseEventAggregator: BaseEventAggregator{eventName: a_hall.EventItemGet},
	}
}

func (a *ItemGetAggregator) AddItem(teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
}

func (a *ItemGetAggregator) HasData() bool {
	return true
}

func (a *ItemGetAggregator) Report(playerId string, teItem *a_hall.TeItemInfo) {
	items := make([]*model.TeItem, 0, len(teItem.Items))
	for _, v := range teItem.Items {
		items = append(items, &model.TeItem{
			Item:           v.Item,
			ItemCount:      v.ItemCount,
			ItemDeltaCount: v.ItemDeltaCount,
		})
	}
	record := model.TeItemGetRecord{
		DefaultHeader: teItem.DefaultHeader,
		TeItemInfo: &model.TeItemInfo{
			Items:       items,
			Source:      teItem.Source,
			ChangeType:  teItem.ChangeType,
			OperateTime: teItem.OperateTime,
		},
	}

	services.GTeService.Track(playerId, a.GetEventName(), &record, teItem.OperateTime.Unix())

}
