package item

import (
	"context"
	"encoding/json"
	"fmt"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_hall"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
)

// 全局处理器管理器
var gItemProcessorManager *ItemProcessorManager

// 初始化处理器管理器
func init() {
	gItemProcessorManager = NewItemProcessorManager()
}

// ProcessItemMessage 处理道具消息
func ProcessItemMessage(ctx context.Context, data []byte) error {
	logrus.Printf("ProcessItemMessage: %s", string(data))

	var teItem a_hall.TeItemInfo
	err := json.Unmarshal(data, &teItem)
	if err != nil {
		logrus.Errorf("ProcessItemMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return fmt.Errorf("unmarshal item message: %w", err)
	}

	playerId := cast.ToString(teItem.PlayerId)

	// 使用处理器管理器处理所有物品并聚合上报
	gItemProcessorManager.ProcessItems(playerId, &teItem)
	return nil
}

// EventAggregator 事件聚合器接口
type EventAggregator interface {
	// AddItem 添加物品到聚合器
	AddItem(teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo)
	// HasData 是否有数据需要上报
	HasData() bool
	// Report 上报聚合数据
	Report(playerId string, teItem *a_hall.TeItemInfo)
	// GetEventName 获取事件名称
	GetEventName() string
}

// ItemProcessor 道具处理器接口
type ItemProcessor interface {
	// ShouldProcess 判断是否应该处理该道具
	ShouldProcess(teItem *a_hall.TeItemInfo, item *commonPB.ItemInfo) bool
	// CollectData 收集处理数据
	CollectData(aggregators map[string]EventAggregator, teItem *a_hall.TeItemInfo, item *commonPB.ItemInfo)
}

// BaseEventAggregator 基础事件聚合器
type BaseEventAggregator struct {
	eventName string
}

func (a *BaseEventAggregator) GetEventName() string {
	return a.eventName
}

// ItemProcessorManager 道具处理器管理
type ItemProcessorManager struct {
	processors []ItemProcessor
}

// NewItemProcessorManager 创建道具处理器管理
func NewItemProcessorManager() *ItemProcessorManager {
	return &ItemProcessorManager{
		processors: []ItemProcessor{
			&ItemGetProcessor{},
			&ItemConsumeProcessor{},
			// 暂时不处理货币和商店上报
			// &TokenGetProcessor{},
			// &TokenConsumeProcessor{},
			// &ShopBuyProcessor{},
		},
	}
}

// RegisterProcessor 注册处理器
func (m *ItemProcessorManager) RegisterProcessor(processor ItemProcessor) {
	m.processors = append(m.processors, processor)
}

// ProcessItems 处理所有道具并聚合上报
func (m *ItemProcessorManager) ProcessItems(playerId string, teItemInfo *a_hall.TeItemInfo) {
	// 使用map收集不同事件类型的聚合器
	aggregators := make(map[string]EventAggregator)

	// 收集所有道具数据
	for _, item := range teItemInfo.Items {
		for _, processor := range m.processors {
			if processor.ShouldProcess(teItemInfo, item) {
				processor.CollectData(aggregators, teItemInfo, item)
			}
		}
	}

	// 统一上报各事件类型的数据
	for _, aggregator := range aggregators {
		if aggregator.HasData() {
			aggregator.Report(playerId, teItemInfo)
		}
	}
}
