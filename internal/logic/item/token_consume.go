package item

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_hall"
	"tasrv/internal/model"
	"tasrv/internal/services"
)

// TokenConsumeAggregator 金币消耗聚合器
type TokenConsumeAggregator struct {
	BaseEventAggregator
	totalCount int64
	source     int32
	hasData    bool
}

func NewTokenConsumeAggregator() *TokenConsumeAggregator {
	return &TokenConsumeAggregator{
		BaseEventAggregator: BaseEventAggregator{eventName: a_hall.EventTokenConsume},
	}
}

func (a *TokenConsumeAggregator) AddItem(teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
	a.totalCount += itemInfo.ItemDeltaCount
	a.source = int32(teItem.Source)
	a.hasData = true
}

func (a *TokenConsumeAggregator) HasData() bool {
	return a.hasData
}

func (a *TokenConsumeAggregator) Report(playerId string, teItem *a_hall.TeItemInfo) {
	record := &model.TeTokenConsumeRecord{
		DefaultHeader: teItem.DefaultHeader,
		TokenCount:    a.totalCount,
		Source:        a.source,
		OperateTime:   teItem.OperateTime,
	}
	services.GTeService.Track(playerId, a.GetEventName(), record, teItem.OperateTime.Unix())
}

// 金币消耗处理器
type TokenConsumeProcessor struct{}

func (p *TokenConsumeProcessor) ShouldProcess(teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) bool {
	return teItem.ChangeType == commonPB.ITEM_OPERATION_IO_REDUCE && itemInfo.Item.ItemType == commonPB.ITEM_TYPE_IT_CURRENCY_COIN
}

func (p *TokenConsumeProcessor) CollectData(aggregators map[string]EventAggregator, teItem *a_hall.TeItemInfo, itemInfo *commonPB.ItemInfo) {
	agg, ok := aggregators[a_hall.EventTokenConsume]
	if !ok {
		agg = NewTokenConsumeAggregator()
		aggregators[a_hall.EventTokenConsume] = agg
	}
	agg.AddItem(teItem, itemInfo)
}
