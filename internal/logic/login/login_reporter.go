package login

import (
	"context"
	"encoding/json"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_login"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"github.com/spf13/cast"
	"tasrv/internal/dao"
	"tasrv/internal/model"
	"tasrv/internal/services"
)

func ProcessLoginMessage(ctx context.Context, data []byte) error {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ProcessLoginMessage: %s", string(data))

	var teLoginInfo a_login.TeLoginInfo
	err := json.Unmarshal(data, &teLoginInfo)
	if err != nil {
		entry.Errorf("ProcessLoginMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return err
	}

	// 转换上报信息
	record := model.TeLoginRecord{
		DefaultHeader: teLoginInfo.DefaultHeader,
		TeLogin:       teLoginInfo.TeLogin,
	}
	playerId := cast.ToString(teLoginInfo.PlayerId)
	// 记录登录时间
	err = new(dao.Tracker).Login(ctx, playerId, record.LastLoginTime)
	if err != nil {
		entry.Errorf("ProcessLoginMessage dao login error, record:%+v, err:%s", record, err)
		return err
	}

	services.GTeService.Track(playerId, teLoginInfo.GetEvent(), &record, teLoginInfo.LastLoginTime.Unix())
	return nil
}

func ProcessRegisterMessage(ctx context.Context, data []byte) error {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ProcessRegisterMessage: %s", string(data))

	var teRegisterInfo a_login.TeRegisterInfo
	err := json.Unmarshal(data, &teRegisterInfo)
	if err != nil {
		entry.Errorf("ProcessRegisterMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return err
	}

	playerId := cast.ToString(teRegisterInfo.PlayerId)

	// 转换上报信息
	record := model.TeRegisterRecord{
		DefaultHeader: teRegisterInfo.DefaultHeader,
		TeRegister:    teRegisterInfo.TeRegister,
	}

	services.GTeService.UserSetOnce(playerId, &record)
	services.GTeService.Track(playerId, teRegisterInfo.GetEvent(), &record, teRegisterInfo.RegisterTime.Unix())
	return nil
}

func ProcessLogoutMessage(ctx context.Context, data []byte) error {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ProcessLogoutMessage: %s", string(data))

	var teLogoutInfo a_login.TeLogoutInfo
	err := json.Unmarshal(data, &teLogoutInfo)
	if err != nil {
		entry.Errorf("ProcessLogoutMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return err
	}

	playerId := cast.ToString(teLogoutInfo.PlayerId)

	// 获取所有时间数据
	playerTimeInfo, err := new(dao.Tracker).LogoutWithAllData(ctx, playerId)
	if err != nil || playerTimeInfo == nil {
		entry.Errorf("ProcessLogoutMessage dao logout error, teLogoutInfo:%+v, err:%+v", teLogoutInfo, err)
		return err
	}

	// 计算本次游玩时长
	var onlineTime int64
	if playerTimeInfo.LoginTime > 0 {
		onlineTime = teLogoutInfo.LogoutTime.Unix() - playerTimeInfo.LoginTime
		if onlineTime < 0 {
			onlineTime = 0
		}
	}

	// 计算总游戏时长
	totalPlayTime := playerTimeInfo.TotalPlayTime
	if totalPlayTime < 0 {
		totalPlayTime = 0
	}
	// 如果没被清空 累计计算钓场时长
	if playerTimeInfo.FishingEnterTime > 0 {
		if st := teLogoutInfo.LogoutTime.Unix() - playerTimeInfo.FishingEnterTime; st > 0 {
			totalPlayTime += st
		}
	}

	// 转换上报信息
	record := model.TeLogoutRecord{
		DefaultHeader:  teLogoutInfo.DefaultHeader,
		TeLogout:       teLogoutInfo.TeLogout,
		ThisOnlineTime: onlineTime,
		PlayTime:       totalPlayTime,
	}

	services.GTeService.Track(playerId, teLogoutInfo.GetEvent(), &record, teLogoutInfo.LogoutTime.Unix())
	return nil
}
