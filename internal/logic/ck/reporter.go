package ck

import (
	"context"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/ta"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

// ReportHandler 定义了处理特定报告类型的接口。
type ReportHandler interface {
	Handle(ctx context.Context, reportData string) error
}

var reportHandlerRegistry = make(map[commonPB.DATA_REPORT_TYPE]ReportHandler)

func init() {
	reportHandlerRegistry[commonPB.DATA_REPORT_TYPE_DRT_NOVICE_SCENE] = NewNoviceSceneHandler()
	reportHandlerRegistry[commonPB.DATA_REPORT_TYPE_DRT_FISHING] = NewFishingHandler()
}

// GetReportHandler 从注册表中检索给定报告类型的处理器。
func GetReportHandler(reportType commonPB.DATA_REPORT_TYPE) (ReportHandler, error) {
	handler, exists := reportHandlerRegistry[reportType]
	if !exists {
		return nil, fmt.Errorf("no handler registered for report type: %s", reportType.String())
	}
	return handler, nil
}

// ProcessAndPublish 处理传入的数据报告并将其发布到 Kafka。
func ProcessAndPublish(ctx context.Context, req *taPB.DataReportReq) error {
	entry := logx.NewLogEntry(ctx)
	playerID := interceptor.GetRPCOptions(ctx).PlayerId
	entry.Infof("Processing CK report for player %d, type %s, data: %s", playerID, req.ReportType.String(), req.ReportData)

	// 获取处理器
	handler, err := GetReportHandler(req.ReportType)
	if err != nil {
		return err
	}

	err = handler.Handle(ctx, req.ReportData)
	if err != nil {
		return err
	}

	entry.Infof("Successfully processed and published report type %s for player %d ", req.ReportType.String(), playerID)
	return nil
}
