package ck

import (
	"context"
	"strings"
	login "tasrv/internal/logic/novice"
	"tasrv/internal/model/clickhouse"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
)

type NoviceSceneHandler struct{}

func NewNoviceSceneHandler() *NoviceSceneHandler {
	return &NoviceSceneHandler{}
}

// Handle 处理新手场景的报告数据。
func (h *NoviceSceneHandler) Handle(ctx context.Context, reportData string) error {

	header := recordx.NewDefaultHeaderFromCtx(ctx)
	serializedBody := recordx.SerializeData(header, dataModel)

	tempData := strings.Split(serializedBody, "|")
	if len(tempData) >= 3 {

	}

	return login.ProcessNoviceMessage()
}
