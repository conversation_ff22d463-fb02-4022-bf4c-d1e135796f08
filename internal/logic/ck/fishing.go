package ck

import (
	"context"
	"tasrv/internal/model/clickhouse"
	"tasrv/internal/pubsub"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

type FishingHandler struct{}

func NewFishingHandler() *FishingHandler {
	return &FishingHandler{}
}

// Handle 处理钓鱼的报告数据。
func (h *FishingHandler) Handle(ctx context.Context, reportData string) error {
	playerID := interceptor.GetRPCOptions(ctx).PlayerId
	dataModel := &clickhouse.RFishing{RawData: reportData}

	// 序列化
	header := recordx.NewDefaultHeaderFromCtx(ctx)
	serializedBody := recordx.SerializeData(header, dataModel)

	// 生产消息
	err := pubsub.GetProducer().SendWithTopic(ctx, dataModel.GetTableName(),
		event.NewMessage(transform.Uint642Str(playerID), []byte(serializedBody)))

	return err
}
