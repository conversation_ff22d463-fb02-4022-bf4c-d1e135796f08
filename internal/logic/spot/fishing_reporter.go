package spot

import (
	"context"
	"encoding/json"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_spot"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"tasrv/internal/dao"
)

// ProcessEnterFishingAreaMessage 处理进入钓场事件
func ProcessEnterFishingAreaMessage(ctx context.Context, data []byte) error {
	logrus.Printf("ProcessEnterFishingAreaMessage: %s", string(data))

	var fishingEnterInfo a_spot.TeFishingRoomEnterInfo
	err := json.Unmarshal(data, &fishingEnterInfo)
	if err != nil {
		logrus.Errorf("ProcessEnterFishingAreaMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return fmt.Errorf("unmarshal enter fishing area message: %w", err)
	}

	playerId := cast.ToString(fishingEnterInfo.PlayerId)

	// 只记录进入钓场时间，不进行事件上报
	fishingTracker := &dao.FishingTracker{}
	err = fishingTracker.EnterFishingArea(ctx, playerId, fishingEnterInfo.TeFishingRoom)
	if err != nil {
		logrus.Errorf("ProcessEnterFishingAreaMessage dao enter fishing area error, fishingEnterInfo:%+v, err:%s", fishingEnterInfo, err)
		return fmt.Errorf("failed to record enter fishing area: %w", err)
	}

	return nil
}

// ProcessExitFishingAreaMessage 处理退出钓场事件
func ProcessExitFishingAreaMessage(ctx context.Context, data []byte) error {
	logrus.Printf("ProcessExitFishingAreaMessage: %s", string(data))

	var fishingExitInfo a_spot.TeFishingRoomExitInfo
	err := json.Unmarshal(data, &fishingExitInfo)
	if err != nil {
		logrus.Errorf("ProcessExitFishingAreaMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return fmt.Errorf("unmarshal exit fishing area message: %w", err)
	}

	playerId := cast.ToString(fishingExitInfo.PlayerId)

	// 处理退出钓场，计算并累加游玩时间
	fishingTracker := &dao.FishingTracker{}
	if err = fishingTracker.ExitFishingArea(ctx, playerId, fishingExitInfo.TeFishingRoom); err != nil {
		logrus.Errorf("ProcessExitFishingAreaMessage dao exit fishing area error, fishingExitInfo:%+v, err:%s", fishingExitInfo, err)
		return err
	}
	return nil
}
