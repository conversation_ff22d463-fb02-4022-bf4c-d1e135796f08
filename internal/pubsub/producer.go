package pubsub

import (
	"sync"

	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/sirupsen/logrus"
)

var (
	producer     event.Sender
	producerOnce sync.Once
)

// InitProducer 初始化消息生产者，确保只初始化一次
func InitProducer() {
	producerOnce.Do(func() {
		sender, err := event.NewKafkaSender()
		if err != nil {
			logrus.Fatalf("创建Kafka发送器失败: %s", err)
		}
		producer = sender
	})
}

// GetProducer 返回初始化后的消息生产者实例
func GetProducer() event.Sender {
	return producer
}
