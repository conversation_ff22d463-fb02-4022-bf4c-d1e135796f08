package model

import (
	"encoding/json"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_login"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

type TeLoginRecord struct {
	*analyze.DefaultHeader
	*a_login.TeLogin
}

func (te *TeLoginRecord) GetIP() string {
	return te.IP
}

func (te *TeLoginRecord) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	data, _ := json.Marshal(te)
	json.Unmarshal(data, &m)
	return m
}

type TeRegisterRecord struct {
	*analyze.DefaultHeader
	*a_login.TeRegister
}

func (te *TeRegisterRecord) ToMap() map[string]interface{} {
	data, _ := json.Marshal(te)
	m := make(map[string]interface{})
	json.Unmarshal(data, &m)
	return m
}

type TeLogoutRecord struct {
	*analyze.DefaultHeader
	*a_login.TeLogout
	ThisOnlineTime int64 `json:"this_online_time"` // 本次在线时长(s)
	PlayTime       int64 `json:"play_time"`        // 游戏时长(s)
}

func (te *TeLogoutRecord) ToMap() map[string]interface{} {
	data, _ := json.Marshal(te)
	m := make(map[string]interface{})
	json.Unmarshal(data, &m)
	return m
}
func NewPlayerTimeInfo() *PlayerTimeInfo {
	return &PlayerTimeInfo{}
}

type PlayerTimeInfo struct {
	LoginTime        int64 `json:"login_time"`
	FishingEnterTime int64 `json:"fishing_enter_time"`
	TotalPlayTime    int64 `json:"total_play_time"`
}

func (p *PlayerTimeInfo) GetLoginTimeName() string {
	return "login_time"
}

func (p *PlayerTimeInfo) GetFishingEnterTimeName() string {
	return "fishing_enter_time"
}

func (p *PlayerTimeInfo) GetTotalPlayTimeName() string {
	return "total_play_time"
}

func NewPlayerTimeInfoFromRdsField(rdsHash map[string]string) *PlayerTimeInfo {
	if rdsHash == nil {
		return nil
	}

	playerTimeInfo := &PlayerTimeInfo{}

	err := transform.Map2Struct(rdsHash, playerTimeInfo)
	if err != nil {
		logrus.Errorf("transform map:%+v to struct error:%v", rdsHash, err)
		return nil
	}

	return playerTimeInfo
}
