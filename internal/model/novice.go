package model

import (
	"encoding/json"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
)

type TeNoviceRecord struct {
	*analyze.DefaultHeader
	*TeNovice
}

type TeNovice struct {
	PlayerId      uint64 // 玩家id
	GuideId       uint32 // 步骤id
	GuideDuration uint64 // 步骤耗时
}

func (te *TeNoviceRecord) GetEvent() string {
	return "te_novice_scene"
}

func (te *TeNoviceRecord) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	data, _ := json.Marshal(te)
	json.Unmarshal(data, &m)
	return m
}
