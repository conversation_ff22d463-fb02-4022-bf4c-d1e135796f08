package model

import (
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
)

// TeItemGetRecord 道具获得记录
type TeItemGetRecord struct {
	*analyze.DefaultHeader
	*TeItemInfo
}

type TeItemInfo struct {
	Items       []*TeItem                 `json:"items"`        // 道具详情
	Source      commonPB.ITEM_SOURCE_TYPE `json:"source"`       // 来源类型
	ChangeType  commonPB.ITEM_OPERATION   `json:"change_type"`  // 修改类型
	OperateTime time.Time                 `json:"operate_time"` // 操作时间
}
type TeItem struct {
	*commonPB.Item
	ItemCount      int64 `json:"item_count"`       // 存量
	ItemDeltaCount int64 `json:"item_delta_count"` // 增量
}

func (te *TeItemGetRecord) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	data, _ := json.Marshal(te)
	json.Unmarshal(data, &m)
	return m
}

// TeItemConsumeRecord 道具消耗记录
type TeItemConsumeRecord struct {
	*analyze.DefaultHeader
	*TeItemInfo
}

func (te *TeItemConsumeRecord) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	data, _ := json.Marshal(te)
	json.Unmarshal(data, &m)
	return m
}

// TeTokenGetRecord 金币获得记录
type TeTokenGetRecord struct {
	*analyze.DefaultHeader
	TokenCount  int64     `json:"token_count"`  // 金币数量
	Source      int32     `json:"source"`       // 来源类型
	OperateTime time.Time `json:"operate_time"` // 操作时间
}

func (te *TeTokenGetRecord) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	data, _ := json.Marshal(te)
	json.Unmarshal(data, &m)
	return m
}

// TeTokenConsumeRecord 金币消耗记录
type TeTokenConsumeRecord struct {
	*analyze.DefaultHeader
	TokenCount  int64     `json:"token_count"`  // 金币数量
	Source      int32     `json:"source"`       // 来源类型
	OperateTime time.Time `json:"operate_time"` // 操作时间
}

func (te *TeTokenConsumeRecord) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	data, _ := json.Marshal(te)
	json.Unmarshal(data, &m)
	return m
}

// TeShopBuyRecord 商店购买记录
type TeShopBuyRecord struct {
	*analyze.DefaultHeader
	*TeItemInfo
}

func (te *TeShopBuyRecord) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	data, _ := json.Marshal(te)
	json.Unmarshal(data, &m)
	return m
}
