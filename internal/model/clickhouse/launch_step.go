package clickhouse

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

type RLaunchStep struct {
	PlayerOpenReportRequest
}

func (r *RLaunchStep) GetTableName() string {
	return "r_launch_step"
}

func (r *RLaunchStep) Format() string {
	return recordx.MarshalWithLine(
		r.GetTableName(),
		r.DefaultHeader.String(),
		r.NowDate,
		r.TimeStampValue,
		transform.Uint642Str(r.PlayerId),
		transform.Int642Str(r.LaunchStepId),
		transform.Int642Str(r.LaunchStepDuration),
		r.LaunchFailDetail,
		r.<PERSON>ce<PERSON>ode,
		r.Expand,
	)
}

// PlayerReportRequest 定义了客户端上报数据的结构
type PlayerOpenReportRequest struct {
	TableName string // 表名
	recordx.DefaultHeader
	NowDate            string // 当前时间 20250425
	TimeStampValue     string // 时间戳 1715222222
	PlayerId           uint64 // 玩家id
	LaunchStepId       int64  // 打开步骤id
	LaunchStepDuration int64  // 本步骤耗时（毫秒）
	LaunchFailDetail   string // 失败详情
	DeviceCode         string // 设备码
	Expand             string // 扩展字段
}

type PlayerOpenReportResponse struct {
	Code    int
	Message string
}
