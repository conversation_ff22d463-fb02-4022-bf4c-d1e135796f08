package dao

import (
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/sirupsen/logrus"
	"tasrv/internal/config"
	"tasrv/internal/model"
	"time"
)

type Tracker struct {
}

// Login 记录登录时间
// 用户ID作为唯一标识，并发登录时最后一次登录覆盖前次记录
func (t *Tracker) Login(ctx context.Context, playerId string, loginTime time.Time) error {
	key := config.GetUserTimeKey(playerId)

	// 存储登录时间
	fields := map[string]interface{}{
		model.NewPlayerTimeInfo().GetLoginTimeName(): loginTime.Unix(),
	}

	pipe := redisx.GetPlayerCli().Pipeline()
	pipe.HMSet(ctx, key, fields)
	pipe.Expire(ctx, key, config.UserTimeExpiration)

	_, err := pipe.Exec(ctx)
	return err
}

// LogoutWithAllData 处理登出，使用 HGETALL 一次性获取所有数据并删除整个 key
func (t *Tracker) LogoutWithAllData(ctx context.Context, playerId string) (*model.PlayerTimeInfo, error) {
	key := config.GetUserTimeKey(playerId)

	// 使用 HGETALL 一次性获取所有数据
	allData, err := redisx.GetPlayerCli().HGetAll(ctx, key).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get all user time data: %w", err)
	}

	playerTimeInfo := model.NewPlayerTimeInfoFromRdsField(allData)

	// 删除整个 key
	err = redisx.GetPlayerCli().Del(ctx, key).Err()
	if err != nil {
		logrus.Warnf("Failed to delete user time key %s: %v", key, err)
	}

	return playerTimeInfo, nil
}
