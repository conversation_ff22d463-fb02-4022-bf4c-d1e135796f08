package dao

import (
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_spot"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"tasrv/internal/config"
	"tasrv/internal/model"
)

// FishingTracker 钓场时间追踪器
type FishingTracker struct {
}

// EnterFishingArea 记录进入钓场时间
func (ft *FishingTracker) EnterFishingArea(ctx context.Context, playerId string, req *a_spot.TeFishingRoom) error {
	key := config.GetUserTimeKey(playerId)

	fields := map[string]interface{}{
		model.NewPlayerTimeInfo().GetFishingEnterTimeName(): req.OperateTime.Unix(),
	}

	pipe := redisx.GetPlayerCli().Pipeline()
	pipe.HMSet(ctx, key, fields)
	pipe.Expire(ctx, key, config.UserTimeExpiration)

	_, err := pipe.Exec(ctx)
	return err
}

// ExitFishingArea 处理退出钓场，计算本次游玩时长并累加到总时长
func (ft *FishingTracker) ExitFishingArea(ctx context.Context, playerId string, req *a_spot.TeFishingRoom) error {
	key := config.GetUserTimeKey(playerId)

	// 获取玩家所有时间数据
	allData, err := redisx.GetPlayerCli().HGetAll(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("failed to get all user time data: %w", err)
	}

	playerTimeInfo := model.NewPlayerTimeInfoFromRdsField(allData)

	// 计算本次游玩时长
	sessionTime := req.OperateTime.Unix() - playerTimeInfo.FishingEnterTime
	if sessionTime < 0 {
		// 处理时间穿越异常
		sessionTime = 0
	}

	// 使用Pipeline批量更新数据
	pipe := redisx.GetPlayerCli().Pipeline()
	updateFields := map[string]interface{}{
		playerTimeInfo.GetFishingEnterTimeName(): 0,
		playerTimeInfo.GetTotalPlayTimeName():    playerTimeInfo.TotalPlayTime + sessionTime,
	}
	pipe.HMSet(ctx, key, updateFields)
	pipe.Expire(ctx, key, config.UserTimeExpiration)
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to update fishing exit data: %w", err)
	}

	return nil
}
